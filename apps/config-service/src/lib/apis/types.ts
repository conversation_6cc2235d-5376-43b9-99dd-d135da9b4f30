export interface MetadataParams {
  [key: string]: unknown;
  page?: number;
  limit?: number;
  search?: string;
  orderBy?: string;
  orderDirection?: "asc" | "desc";
}

export type OrderDirection = "asc" | "desc" | "ASC" | "DESC" | undefined;

export type SortMetadataParams = {
  orderBy?: string;
  orderDirection?: OrderDirection;
};

export interface ListBaseResponse<T> {
  results: T[];
  metadata: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    itemsPerPage: number;
  };
}
