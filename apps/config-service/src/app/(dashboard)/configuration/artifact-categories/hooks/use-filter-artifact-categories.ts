import {
  parseAsBoolean,
  parseAsInteger,
  parseAsString,
  useQueryState,
} from "nuqs";

export const useFilterArtifactCategories = () => {
  const [tmfZoneName, setTmfZoneName] = useQueryState(
    "tmfZoneName",
    parseAsString,
  );
  const [tmfSectionName, setTmfSectionName] = useQueryState(
    "tmfSectionName",
    parseAsString,
  );
  const [tmfRecordGroupName, setTmfRecordGroupName] = useQueryState(
    "tmfRecordGroupName",
    parseAsString,
  );
  const [isfZoneName, setIsfZoneName] = useQueryState(
    "isfZoneName",
    parseAsString,
  );
  const [isfSectionName, setIsfSectionName] = useQueryState(
    "isfSectionName",
    parseAsString,
  );
  const [isfRecordGroupName, setIsfRecordGroupName] = useQueryState(
    "isfRecordGroupName",
    parseAsString,
  );
  const [recordType, setRecordType] = useQueryState(
    "recordType",
    parseAsString,
  );
  const [alternativeNames, setAlternativeNames] = useQueryState(
    "alternativeNames",
    parseAsString,
  );
  const [isTMF, setIsTMF] = useQueryState("isTMF", parseAsBoolean);
  const [isISF, setIsISF] = useQueryState("isISF", parseAsBoolean);
  const [isActive, setIsActive] = useQueryState("isActive", parseAsBoolean);
  const [page, setPage] = useQueryState("page", parseAsInteger);

  const [requiresSignature, setRequiresSignature] = useQueryState(
    "requiresSignature",
    parseAsBoolean,
  );
  const [expires, setExpires] = useQueryState("expires", parseAsBoolean);
  const [inspectableRecord, setInspectableRecord] = useQueryState(
    "inspectableRecord",
    parseAsBoolean,
  );
  const [includesPHI, setIncludesPHI] = useQueryState(
    "includesPHI",
    parseAsBoolean,
  );

  return {
    tmfZoneName,
    setTmfZoneName,
    tmfSectionName,
    setTmfSectionName,
    tmfRecordGroupName,
    setTmfRecordGroupName,
    isfZoneName,
    setIsfZoneName,
    isfSectionName,
    setIsfSectionName,
    isfRecordGroupName,
    setIsfRecordGroupName,
    recordType,
    setRecordType,
    alternativeNames,
    setAlternativeNames,
    isTMF,
    setIsTMF,
    isISF,
    setIsISF,
    isActive,
    setIsActive,
    requiresSignature,
    setRequiresSignature,
    expires,
    setExpires,
    inspectableRecord,
    setInspectableRecord,
    includesPHI,
    setIncludesPHI,
    page,
    setPage,
  };
};
