"use client";

import { Card } from "flowbite-react";
import { ListFilter } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { HiPlus } from "react-icons/hi";
import { z } from "zod";

import { LayoutContent } from "@/components";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";

import { useFilterArtifactCategories } from "../hooks/use-filter-artifact-categories";
import { CategoriesTable } from "./categories-table";
import { ModalArtifactCategory } from "./category-modal";
import { CategoryVersionModal } from "./category-version-modal";
import { CategoryVersionSelect } from "./category-version-select";
import { FilterModal } from "./filter-modal";
import { ImportExportActions } from "./import-export-actions";

// Schema for the category version selection form
const categoryVersionSchema = z.object({
  selectedVersionId: z.string().optional(),
});

type CategoryVersionFormData = z.infer<typeof categoryVersionSchema>;

export const ArtifactCategoriesContent = () => {
  const [isVersionModalOpen, setIsVersionModalOpen] = useState(false);
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [selectedVersionId, setSelectedVersionId] = useState<string>("");

  const {
    tmfZoneName,
    tmfSectionName,
    tmfRecordGroupName,
    isfZoneName,
    isfSectionName,
    isfRecordGroupName,
    recordType,
    alternativeNames,
    isTMF,
    isISF,
    isActive,
    requiresSignature,
    expires,
    inspectableRecord,
    includesPHI,
  } = useFilterArtifactCategories();

  // Create form instance to watch for changes
  const formMethods = useForm<CategoryVersionFormData>({
    defaultValues: { selectedVersionId: "" },
    mode: "onChange",
  });

  const { watch } = formMethods;

  // Watch for form changes and update selectedVersionId
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      // Update selectedVersionId when the form field changes
      if (name === "selectedVersionId") {
        const newValue = value.selectedVersionId || "";
        setSelectedVersionId(newValue);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]); // Intentionally not including selectedVersionId to avoid infinite loops

  const handleAddCategory = () => {
    setSelectedCategory(null);
    setIsCategoryModalOpen(true);
  };

  const handleEditCategory = (category: any) => {
    setSelectedCategory(category);
    setIsCategoryModalOpen(true);
  };

  const handleCloseCategoryModal = () => {
    setIsCategoryModalOpen(false);
    setSelectedCategory(null);
  };

  const handleVersionFormSubmit = (_data: CategoryVersionFormData) => {
    // Form submission is handled automatically via the watch effect
    // This function is required by the Form component but doesn't need to do anything
  };

  // Applied filters for display
  const appliedFilters = [
    { label: "TMF Zone", value: tmfZoneName, key: "tmfZoneName" },
    { label: "TMF Section", value: tmfSectionName, key: "tmfSectionName" },
    {
      label: "TMF Record Group",
      value: tmfRecordGroupName,
      key: "tmfRecordGroupName",
    },
    { label: "ISF Zone", value: isfZoneName, key: "isfZoneName" },
    { label: "ISF Section", value: isfSectionName, key: "isfSectionName" },
    {
      label: "ISF Record Group",
      value: isfRecordGroupName,
      key: "isfRecordGroupName",
    },
    { label: "Record Type", value: recordType, key: "recordType" },
    {
      label: "Alternative Names",
      value: alternativeNames,
      key: "alternativeNames",
    },
    { label: "TMF", value: isTMF, key: "isTMF" },
    { label: "ISF", value: isISF, key: "isISF" },
    { label: "Active", value: isActive, key: "isActive" },
    {
      label: "Requires Signature",
      value: requiresSignature,
      key: "requiresSignature",
    },
    { label: "Expires", value: expires, key: "expires" },
    {
      label: "Inspectable Record",
      value: inspectableRecord,
      key: "inspectableRecord",
    },
    { label: "Includes PHI", value: includesPHI, key: "includesPHI" },
  ];

  const countedFilters = appliedFilters.filter(
    (filter) =>
      filter.value !== null &&
      filter.value !== undefined &&
      filter.value !== "",
  );

  const handleOpenFilterModal = () => {
    setIsFilterModalOpen(true);
  };

  return (
    <LayoutContent>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="mb-2 text-3xl font-bold text-gray-900">
            Artifact Categories
          </h1>
          <p className="text-gray-500">
            Manage artifact categories and their versions for document
            classification.
          </p>
        </div>

        <Card
          className="mb-6"
          theme={{
            root: {
              children: "p-0",
            },
          }}
        >
          <div className="p-6">
            <div className="mb-2 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">
                Category Version
              </h2>
              <Button
                variant="primary"
                onClick={() => setIsVersionModalOpen(true)}
                className="flex items-center gap-2"
              >
                <HiPlus className="h-4 w-4" />
                Add New Version
              </Button>
            </div>
            <Form
              schema={categoryVersionSchema}
              onSubmit={handleVersionFormSubmit}
              formMethods={formMethods}
            >
              <CategoryVersionSelect />
            </Form>
          </div>
        </Card>

        <Card
          theme={{
            root: {
              children: "p-0",
            },
          }}
        >
          <div className="">
            <div className="mb-1 flex items-center justify-between p-6">
              <h2 className="text-xl font-semibold text-gray-900">
                Artifact Categories
              </h2>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={handleOpenFilterModal}
                  className="flex items-center gap-2"
                >
                  <ListFilter className="h-4 w-4" />
                  Filter
                  {countedFilters.length > 0 && (
                    <span className="ml-1 flex size-6 items-center justify-center rounded-full bg-blue-600 text-xs font-semibold text-white">
                      {countedFilters.length}
                    </span>
                  )}
                </Button>
                <ImportExportActions selectedVersionId={selectedVersionId} />
                <Button
                  variant="primary"
                  onClick={handleAddCategory}
                  disabled={!selectedVersionId}
                  className="flex items-center gap-2"
                >
                  <HiPlus className="h-4 w-4" />
                  Add Category
                </Button>
              </div>
            </div>

            {selectedVersionId ? (
              <CategoriesTable
                versionId={selectedVersionId}
                onEditCategory={handleEditCategory}
              />
            ) : (
              <div className="rounded-lg bg-gray-50 p-8 text-center">
                <p className="text-gray-500">
                  Please select a category version to view and manage artifact
                  categories.
                </p>
              </div>
            )}
          </div>
        </Card>

        {/* Modals */}
        {isVersionModalOpen && (
          <CategoryVersionModal
            isOpen
            onClose={() => setIsVersionModalOpen(false)}
            selectedVersion={null}
          />
        )}

        {isCategoryModalOpen && (
          <ModalArtifactCategory
            isOpen
            onClose={handleCloseCategoryModal}
            artifactCategory={selectedCategory}
            categoryVersionId={selectedVersionId}
          />
        )}

        {isFilterModalOpen && (
          <FilterModal
            isOpen={isFilterModalOpen}
            onClose={() => setIsFilterModalOpen(false)}
          />
        )}
      </div>
    </LayoutContent>
  );
};
