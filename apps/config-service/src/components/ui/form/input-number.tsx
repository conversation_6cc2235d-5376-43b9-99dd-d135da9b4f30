"use client";

import { Minus, Plus } from "lucide-react";
import { ComponentPropsWithoutRef, ElementRef, forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { NumericFormat } from "react-number-format";

import { cn } from "@/lib/utils";

import { Button } from "../button";

const StyledInput = forwardRef<
  ElementRef<"input">,
  ComponentPropsWithoutRef<"input"> & {
    isError?: boolean;
  }
>(({ isError, className, ...props }, ref) => (
  <input
    {...props}
    ref={ref}
    className={cn(
      "w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-center text-sm read-only:bg-gray-200 focus:border-blue-500 focus:ring-blue-500",
      isError && "!border-red-500 !ring-red-500",
      className,
    )}
  />
));

StyledInput.displayName = "StyledInput";

type Props = {
  name: string;
  isShowButtons?: boolean;
  isAllowNegative?: boolean;
  isAllowDecimalNumber?: boolean;
  min?: number;
  max?: number;
  step?: number;
} & ComponentPropsWithoutRef<"input">;

const InputNumber = ({
  name,
  isShowButtons = false,
  isAllowNegative = true,
  isAllowDecimalNumber = true,
  min,
  max,
  placeholder,
  id,
  step = 1,
  className,
}: Props) => {
  const { control } = useFormContext();

  const allowNegative =
    typeof min === "number" && min >= 0 ? false : isAllowNegative;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { ref, ...rest }, formState: { errors } }) => {
        const errorMessage = errors[name]?.message?.valueOf();
        const isError = typeof errorMessage === "string";

        const currentValue = !rest.value
          ? 0
          : isAllowDecimalNumber
            ? +parseFloat(rest.value).toFixed(2)
            : parseInt(rest.value);

        const isDisableDecrease =
          typeof min === "number" && currentValue <= min;

        const isDisableIncrease =
          typeof max === "number" && currentValue >= max;

        const handleDecrement = () => {
          if (isDisableDecrease) return;
          rest.onChange(currentValue - step);
        };
        const handleIncrease = () => {
          if (isDisableIncrease) return;
          rest.onChange(currentValue + step);
        };

        return (
          <>
            <div className="flex w-full items-center gap-[6px]">
              <Button
                onClick={handleDecrement}
                variant="outline"
                className={cn(
                  "!size-11 cursor-pointer",
                  !isShowButtons && "hidden",
                )}
                disabled={isDisableDecrease}
                type="button"
              >
                <Minus />
              </Button>
              <NumericFormat
                {...rest}
                min={min}
                max={max}
                placeholder={placeholder}
                allowNegative={allowNegative}
                customInput={StyledInput}
                getInputRef={ref}
                decimalScale={!isAllowDecimalNumber ? 0 : 2}
                id={id}
                inputMode="numeric"
                isError={isError}
                onKeyDown={(e) => {
                  if (e.key === "ArrowUp") {
                    handleIncrease();
                  }
                  if (e.key === "ArrowDown") {
                    handleDecrement();
                  }
                }}
                isAllowed={(values) => {
                  const value = values.floatValue;
                  if (typeof max === "number" && value) {
                    return value <= max;
                  }
                  if (typeof min === "number" && value) {
                    return value >= min;
                  }
                  return true;
                }}
                className={cn(
                  className,
                  !isShowButtons && "[&_input]:text-left",
                )}
              />
              <Button
                variant="outline"
                type="button"
                className={cn(
                  "!size-11 cursor-pointer",
                  !isShowButtons && "hidden",
                )}
                onClick={handleIncrease}
                disabled={isDisableIncrease}
              >
                <Plus />
              </Button>
            </div>
            {isError && (
              <div className={cn("flex", isShowButtons && "justify-center")}>
                <span className="text-sm text-red-500">{errorMessage}</span>
              </div>
            )}
          </>
        );
      }}
    />
  );
};

// InputNumber.displayName = "InputNumber";

export { InputNumber };
