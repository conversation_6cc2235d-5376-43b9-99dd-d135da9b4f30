"use client";

import "react-day-picker/style.css";
import "./date-picker.css";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  Placement,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import React, {
  forwardRef,
  useEffect,
  useId,
  useMemo,
  useRef,
  useState,
} from "react";
import { DayPicker } from "react-day-picker";
import { Controller, useFormContext } from "react-hook-form";
import { IoCalendarOutline, IoClose } from "react-icons/io5";
import { useTimescape } from "timescape/react";
import type { Merge } from "type-fest";

import { cn } from "@/lib/utils";

import { Button } from "../button";

// Configuration for input fields
const INPUT_CONFIG = {
  dd: {
    width: "!w-[30px]",
    key: "days",
  },
  mm: {
    width: "!w-[34px]",
    key: "months",
  },
  yyyy: {
    width: "!w-[48px]",
    key: "years",
  },
} as const;

export type DatePickerProps = Merge<
  React.HTMLAttributes<HTMLDivElement>,
  {
    name: string;
    placeholder?: string;
    shouldShowError?: boolean;
    disabled?: boolean;
    className?: string;
    placement?: Placement;
    minDate?: Date;
    maxDate?: Date;
    format?: "yyyy/mm/dd" | "dd/mm/yyyy" | "mm/dd/yyyy";
    /**
     * Array of dates to highlight with orange background in the calendar
     * Useful for marking important dates that need attention
     */
    highlightDates?: Date[];
    /**
     * Whether to include timezone information in the date
     * When set to false, the timezone will be excluded from the date
     * @default true
     */
    enableTimezone?: boolean;
    /**
     * Whether to disable the clear button
     * @default false
     */
    disableClearBtn?: boolean;
    /**
     * Whether to disable the today button
     * @default false
     */
    disableTodayBtn?: boolean;
    /**
     * Custom action buttons to render in the datepicker
     * Useful for adding custom actions like "Clear" or "Today"
     */
    customActionBtns?: React.ReactNode;
  }
>;

// Event name for datepicker toggle events
const DATEPICKER_TOGGLE_EVENT = "datepicker-toggle";

/**
 * Parses an ISO date string with proper timezone handling
 * @param isoString - ISO date string to parse
 * @param enableTimezone - Whether to preserve timezone information
 * @returns Date object with the correct date
 */
const parseISODateString = (
  isoString: string,
  enableTimezone: boolean = true,
): Date => {
  // For "invalid-date" marker, return current date
  if (isoString === "invalid-date") {
    return new Date();
  }

  try {
    if (enableTimezone) {
      // When timezone is enabled, parse the date normally to preserve timezone
      return new Date(isoString);
    } else {
      // When timezone is disabled, extract just the date part to avoid timezone shifts
      const datePart = isoString.split("T")[0];
      const [year, month, day] = datePart.split("-").map(Number);

      // Create a date at noon UTC to avoid any timezone issues
      return new Date(Date.UTC(year, month - 1, day, 12, 0, 0, 0));
    }
  } catch (error) {
    console.error("Error parsing ISO date:", error);
    return new Date(isoString); // Fallback to standard parsing
  }
};

// InputDate component for manual date entry
const InputDate = forwardRef<
  HTMLInputElement,
  {
    setIsOpen: (isOpen: boolean) => void;
    value?: string;
    onChange?: (value?: string | null) => void;
    minDate?: Date;
    maxDate?: Date;
    setHasInteracted: () => void;
    hasError?: boolean;
    disabled?: boolean;
    format?: "yyyy/mm/dd" | "dd/mm/yyyy" | "mm/dd/yyyy";
    enableTimezone?: boolean;
  }
>(
  (
    {
      setIsOpen,
      value,
      onChange,
      minDate,
      maxDate,
      setHasInteracted,
      hasError,
      disabled,
      format = "yyyy/mm/dd",
      enableTimezone = true,
    },
    _,
  ) => {
    const isInternalUpdate = useRef(false);
    const prevValueRef = useRef(value);
    const [isFocused, setIsFocused] = useState(false);

    // Helper function to check if a date is within the allowed range
    const isDateInRange = useMemo(() => {
      return (date: Date): boolean => {
        if (minDate && date < minDate) return false;
        if (maxDate && date > maxDate) return false;
        return true;
      };
    }, [minDate, maxDate]);

    const { getRootProps, getInputProps, update } = useTimescape({
      date: value ? parseISODateString(value, enableTimezone) : undefined,
      onChangeDate: (nextDate: any) => {
        if (!isInternalUpdate.current) {
          // Only allow dates within the min/max range
          if (nextDate && !isDateInRange(nextDate)) {
            // If date is outside range, don't update and trigger validation error
            // Set an invalid date string to trigger form validation error
            onChange?.("invalid-date");
            setHasInteracted();
            return;
          }
          onChange?.(nextDate ? nextDate.toISOString() : undefined);
          setHasInteracted();
        }
      },
      minDate,
      maxDate,
      digits: "2-digit" as const, // Ensure consistent display format
    });

    // Sync the input value with the calendar selection
    useEffect(() => {
      if (prevValueRef.current !== value) {
        prevValueRef.current = value;
        isInternalUpdate.current = true;

        // Skip update for invalid date marker
        if (value === "invalid-date") {
          setTimeout(() => {
            isInternalUpdate.current = false;
          }, 0);
          return;
        }

        // Validate the new date is within range before updating
        const newDate = value
          ? parseISODateString(value, enableTimezone)
          : undefined;
        if (!newDate || isDateInRange(newDate)) {
          update((state) => ({
            ...state,
            date: newDate,
          }));
        }

        setTimeout(() => {
          isInternalUpdate.current = false;
        }, 0);
      }
    }, [value, update, isDateInRange, enableTimezone]);

    // Determine the order of date parts based on format
    const order = useMemo(() => {
      switch (format) {
        case "yyyy/mm/dd":
          return ["yyyy", "mm", "dd"] as const;
        case "mm/dd/yyyy":
          return ["mm", "dd", "yyyy"] as const;
        case "dd/mm/yyyy":
        default:
          return ["dd", "mm", "yyyy"] as const;
      }
    }, [format]);

    const handleInputFocus = () => {
      setIsFocused(true);
    };

    return (
      <div
        className={cn(
          "timescape-root group relative h-[42px] !w-full !rounded-lg !border-gray-300 bg-gray-50 !px-2 focus-within:!outline-none focus-visible:!outline-none",
          !disabled && "cursor-pointer",
          disabled && "cursor-not-allowed opacity-70",
          isFocused &&
            !disabled &&
            "!border-blue-500 !bg-blue-50 !ring-1 !ring-blue-500",
          hasError &&
            !disabled &&
            "!border-red-500 !bg-red-50 !ring-0 !ring-red-500",
        )}
        {...(getRootProps() as any)}
        onClick={(e) => {
          if (!disabled) {
            setIsOpen(true);
            setHasInteracted();
          }
          e.stopPropagation();
        }}
        aria-hidden
      >
        {order.map((part, idx) => (
          <React.Fragment key={part}>
            <input
              className={cn(
                "timescape-input bg-inherit !text-center !text-sm placeholder:text-gray-500",
                !disabled &&
                  "focus:bg-primary-600 !cursor-text focus:rounded-lg focus:text-white focus:placeholder:text-white",
                disabled && "cursor-not-allowed",
                INPUT_CONFIG[part].width,
              )}
              {...getInputProps(INPUT_CONFIG[part].key)}
              disabled={disabled}
              onFocus={handleInputFocus}
              onBlur={() => setIsFocused(false)}
            />
            {idx < order.length - 1 && <span className="separator">/</span>}
          </React.Fragment>
        ))}
        {value ? (
          <IoClose
            className={cn(
              "absolute right-2 top-1/2 -translate-y-1/2",
              hasError ? "text-red-500" : isFocused ? "text-blue-500" : "",
            )}
            onClick={(e: React.MouseEvent<SVGElement>) => {
              if (disabled) return;
              e.stopPropagation();
              setHasInteracted();
              onChange?.(null);
            }}
          />
        ) : (
          <IoCalendarOutline
            className={cn(
              "absolute right-2 top-1/2 -translate-y-1/2",
              hasError ? "text-red-500" : isFocused ? "text-blue-500" : "",
            )}
          />
        )}
      </div>
    );
  },
);
InputDate.displayName = "InputDate";

const DatePicker = forwardRef<HTMLDivElement, DatePickerProps>(
  (
    {
      name,
      disabled,
      className,
      minDate,
      maxDate,
      customActionBtns,
      highlightDates = [],
      shouldShowError = true,
      placeholder = "Select date",
      placement = "bottom-start",
      format = "yyyy/mm/dd",
      enableTimezone = true,
      disableClearBtn = false,
      disableTodayBtn = false,
      ...props
    },
    ref,
  ) => {
    const { control, watch } = useFormContext();
    const [isOpen, setIsOpen] = useState(false);

    // Initialize month based on first highlighted date, field value, or current date
    const initialMonth = useMemo(() => {
      // First try to use the first valid highlighted date
      if (highlightDates && highlightDates.length > 0) {
        const validHighlightDate = highlightDates.find(
          (date) => date instanceof Date && !isNaN(date.getTime()),
        );
        if (validHighlightDate) {
          return validHighlightDate;
        }
      }

      // Default to current date if no highlighted dates
      return new Date();
    }, [highlightDates]);

    const [month, setMonth] = useState<Date>(initialMonth);

    // Watch for changes in the field value
    const fieldValue = watch(name);

    // Update month when field value changes
    useEffect(() => {
      if (fieldValue && fieldValue !== "invalid-date") {
        try {
          const date = parseISODateString(fieldValue, enableTimezone);
          if (!isNaN(date.getTime())) {
            setMonth(date);
          }
        } catch {
          // Ignore invalid dates
        }
      }
    }, [fieldValue, enableTimezone]);

    // Update month when highlightDates change
    useEffect(() => {
      if (highlightDates && highlightDates.length > 0) {
        const validHighlightDate = highlightDates.find(
          (date) => date instanceof Date && !isNaN(date.getTime()),
        );
        if (validHighlightDate) {
          setMonth(validHighlightDate);
        }
      }
    }, [highlightDates]);

    // Generate unique ID for this datepicker instance
    const datepickerId = useId();

    // Use a ref to track if component has mounted and prevent automatic interaction
    const isMountedRef = useRef(false);
    const userInteractedRef = useRef(false);

    // Handle user interaction safely
    const handleUserInteraction = () => {
      if (isMountedRef.current) {
        userInteractedRef.current = true;
      }
    };

    // Set the mounted ref to true after component mounts
    useEffect(() => {
      isMountedRef.current = true;

      return () => {
        isMountedRef.current = false;
      };
    }, []);

    // Listen for other datepickers opening
    useEffect(() => {
      const handleDatepickerToggle = (e: CustomEvent) => {
        const { id, isOpen: toggledOpen } = e.detail;
        // Close this datepicker if another one is opening
        if (id !== datepickerId && toggledOpen && isOpen) {
          setIsOpen(false);
        }
      };

      // Add event listener with type assertion
      document.addEventListener(
        DATEPICKER_TOGGLE_EVENT,
        handleDatepickerToggle as EventListener,
      );

      return () => {
        document.removeEventListener(
          DATEPICKER_TOGGLE_EVENT,
          handleDatepickerToggle as EventListener,
        );
      };
    }, [datepickerId, isOpen]);

    // Broadcast when this datepicker opens/closes
    useEffect(() => {
      if (isMountedRef.current) {
        const event = new CustomEvent(DATEPICKER_TOGGLE_EVENT, {
          detail: { id: datepickerId, isOpen },
        });
        document.dispatchEvent(event);

        // When opening the date picker, ensure the month is set to show highlighted dates
        if (isOpen && highlightDates && highlightDates.length > 0) {
          const validHighlightDate = highlightDates.find(
            (date) => date instanceof Date && !isNaN(date.getTime()),
          );
          if (validHighlightDate) {
            setMonth(validHighlightDate);
          }
        }
      }
    }, [isOpen, datepickerId, highlightDates]);

    // Add escape key handler
    useEffect(() => {
      if (!isOpen) return;

      const handleEscapeKey = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
          setIsOpen(false);
        }
      };

      document.addEventListener("keydown", handleEscapeKey);
      return () => {
        document.removeEventListener("keydown", handleEscapeKey);
      };
    }, [isOpen]);

    // Setup floating UI for the calendar dropdown
    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: setIsOpen,
      placement: "bottom-start",
      middleware: [
        offset(8),
        flip({
          fallbackPlacements: ["top-start"],
          fallbackStrategy: "bestFit",
          padding: 1,
          crossAxis: false,
        }),
        shift({
          padding: 1,
        }),
      ],
      whileElementsMounted: autoUpdate,
    });

    const { getReferenceProps, getFloatingProps } = useInteractions([
      useClick(context),
      useDismiss(context, {
        outsidePress: true,
        bubbles: false,
        referencePress: false,
      }),
    ]);

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          return (
            <div className={cn("space-y-1", className)} ref={ref} {...props}>
              <div
                className="relative flex h-fit items-center gap-2"
                aria-hidden
              >
                <div
                  ref={refs.setReference}
                  {...getReferenceProps()}
                  className="h-fit w-full"
                >
                  <InputDate
                    setIsOpen={setIsOpen}
                    value={field.value ? field.value : undefined}
                    onChange={(value) => {
                      if (!userInteractedRef.current) {
                        handleUserInteraction();
                        return;
                      }
                      // Handle the invalid-date marker
                      if (value === "invalid-date") {
                        field.onChange("invalid-date");
                      } else if (value) {
                        const date = new Date(value);
                        if (enableTimezone) {
                          // Keep timezone information
                          field.onChange(date.toISOString());
                        } else {
                          // Remove timezone information by setting time to noon UTC
                          // This helps avoid date shifts due to timezone conversions
                          const utcDate = new Date(
                            Date.UTC(
                              date.getFullYear(),
                              date.getMonth(),
                              date.getDate(),
                              12,
                              0,
                              0,
                              0,
                            ),
                          );
                          field.onChange(utcDate.toISOString());
                        }
                      } else {
                        field.onChange(null);
                      }
                      handleUserInteraction();
                    }}
                    minDate={minDate}
                    maxDate={maxDate}
                    key={
                      field.value ? "populated" : "empty"
                    } /* Add key to force re-render */
                    setHasInteracted={handleUserInteraction}
                    hasError={hasError}
                    disabled={disabled}
                    format={format}
                    enableTimezone={enableTimezone}
                  />
                </div>

                {isOpen && (
                  <FloatingPortal>
                    <FloatingFocusManager
                      context={context}
                      modal={false}
                      order={["reference", "content"]}
                      returnFocus={true}
                    >
                      <div
                        ref={refs.setFloating}
                        style={floatingStyles}
                        {...getFloatingProps()}
                        className="z-50 rounded-lg border bg-white shadow-md"
                      >
                        <DayPicker
                          mode="single"
                          defaultMonth={month}
                          month={month}
                          onMonthChange={setMonth}
                          selected={
                            field.value && field.value !== "invalid-date"
                              ? parseISODateString(field.value, enableTimezone)
                              : undefined
                          }
                          onSelect={(date) => {
                            // Check if the selected date is within the allowed range
                            if (!date) return;

                            const isInRange =
                              (!minDate || (date && date >= minDate)) &&
                              (!maxDate || (date && date <= maxDate));

                            if (date && !isInRange && (minDate || maxDate)) {
                              // If date is outside range, set invalid-date to trigger validation error
                              field.onChange("invalid-date");
                            } else if (date) {
                              if (enableTimezone) {
                                // Keep timezone information
                                field.onChange(date.toISOString());
                              } else {
                                // Remove timezone information by setting time to noon UTC
                                // This helps avoid date shifts due to timezone conversions
                                const utcDate = new Date(
                                  Date.UTC(
                                    date.getFullYear(),
                                    date.getMonth(),
                                    date.getDate(),
                                    12,
                                    0,
                                    0,
                                    0,
                                  ),
                                );
                                field.onChange(utcDate.toISOString());
                              }
                            } else {
                              field.onChange(undefined);
                            }

                            handleUserInteraction();
                          }}
                          disabled={
                            typeof disabled === "boolean"
                              ? disabled
                              : [
                                  ...(maxDate
                                    ? [
                                        {
                                          after: maxDate,
                                        },
                                      ]
                                    : []),
                                  ...(minDate
                                    ? [
                                        {
                                          before: minDate,
                                        },
                                      ]
                                    : []),
                                ]
                          }
                          modifiers={{
                            highlighted: highlightDates
                              .filter(
                                (date): date is Date =>
                                  date instanceof Date &&
                                  !isNaN(date.getTime()),
                              )
                              .map(
                                (date) =>
                                  new Date(
                                    date.getFullYear(),
                                    date.getMonth(),
                                    date.getDate(),
                                  ),
                              ),
                            // Add modifier for first highlighted date
                            highlightedFirst: (() => {
                              const validDates = highlightDates.filter(
                                (date): date is Date =>
                                  date instanceof Date &&
                                  !isNaN(date.getTime()),
                              );
                              return validDates.length > 0
                                ? [
                                    new Date(
                                      validDates[0].getFullYear(),
                                      validDates[0].getMonth(),
                                      validDates[0].getDate(),
                                    ),
                                  ]
                                : [];
                            })(),
                            // Add modifier for last highlighted date
                            highlightedLast: (() => {
                              const validDates = highlightDates.filter(
                                (date): date is Date =>
                                  date instanceof Date &&
                                  !isNaN(date.getTime()),
                              );
                              return validDates.length > 0
                                ? [
                                    new Date(
                                      validDates[
                                        validDates.length - 1
                                      ].getFullYear(),
                                      validDates[
                                        validDates.length - 1
                                      ].getMonth(),
                                      validDates[
                                        validDates.length - 1
                                      ].getDate(),
                                    ),
                                  ]
                                : [];
                            })(),
                          }}
                          className="w-fit p-3"
                          classNames={{
                            selected:
                              "!bg-primary-600 !text-white rounded-lg hover:!bg-primary-600 hover:!text-white",
                          }}
                          modifiersClassNames={{
                            highlighted: "rdp-day_highlighted",
                            highlightedFirst: "rdp-day_highlighted-first",
                            highlightedLast: "rdp-day_highlighted-last",
                          }}
                        />

                        <div className="flex justify-between gap-2.5 px-3 pb-3">
                          {!disableTodayBtn && (
                            <Button
                              onClick={() => {
                                const today = new Date();
                                if (enableTimezone) {
                                  // Keep timezone information
                                  field.onChange(today.toISOString());
                                } else {
                                  // Remove timezone information by setting time to noon UTC
                                  const utcToday = new Date(
                                    Date.UTC(
                                      today.getFullYear(),
                                      today.getMonth(),
                                      today.getDate(),
                                      12,
                                      0,
                                      0,
                                      0,
                                    ),
                                  );
                                  field.onChange(utcToday.toISOString());
                                }
                                handleUserInteraction();
                              }}
                              className="flex-1"
                            >
                              Today
                            </Button>
                          )}

                          {!disableClearBtn && (
                            <Button
                              variant="outline"
                              onClick={() => {
                                field.onChange(null);
                                handleUserInteraction();
                              }}
                              className="flex-1"
                            >
                              Clear
                            </Button>
                          )}

                          {customActionBtns}
                        </div>
                      </div>
                    </FloatingFocusManager>
                  </FloatingPortal>
                )}
              </div>

              {hasError && shouldShowError && (
                <span className="error-message text-sm text-red-500">
                  {errorMessage}
                </span>
              )}
            </div>
          );
        }}
      />
    );
  },
);

DatePicker.displayName = "DatePicker";

export { DatePicker };
