const { join } = require("path");
const flowbite = require("flowbite-react/tailwind");
const colors = require("tailwindcss/colors");

const { dirname } = require("path");
const { fileURLToPath } = require("url");

const __dirname = dirname(fileURLToPath(import.meta.url));

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    join(
      __dirname,
      "{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html,css,js}",
    ),
    join(__dirname, "../../packages/shared-ui/src/**/*.{ts,tsx}"),
    flowbite.content({ base: "../../" }),
  ],
  theme: {
    extend: {
      colors: {
        primary: colors.blue,
      },
      fontFamily: {
        "plus-jakarta": ["var(--font-plus-jakarta-sans)"],
        poppins: ["var(--font-poppins)"],
        inter: ["var(--font-inter)"],
      },
    },
  },
  plugins: [flowbite.plugin()],
};
